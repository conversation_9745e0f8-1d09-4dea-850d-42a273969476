#include <iostream>
#include <exception>
#include "core/main_controller.h"
#include "utilities/logger.h"

/**
 * @brief PixivTagDownloader 主程序入口
 *
 * 这是一个用于从Pixiv下载作品的C++应用程序。
 * 支持根据用户指定的标准下载插画、漫画和小说。
 *
 * 主要功能：
 * - 支持交互式和命令行两种操作模式
 * - 并发下载和生产者-消费者模型
 * - 灵活的配置管理和文件组织
 * - 完整的错误处理和日志系统
 * - 支持多种下载方式（直接下载、aria2c）
 * - 可配置的文件命名模板和目录结构
 *
 * @param argc 命令行参数数量
 * @param argv 命令行参数数组
 * @return int 程序退出代码
 */
int main(int argc, char* argv[]) {
    try {
        // 创建主控制器实例
        pixiv_downloader::core::MainController controller;

        // 运行应用程序
        auto exit_code = controller.Run(argc, argv);

        // 返回退出代码
        return static_cast<int>(exit_code);

    } catch (const std::exception& e) {
        // 捕获未处理的异常
        std::cerr << "程序发生未处理的异常: " << e.what() << std::endl;

        // 尝试记录到日志
        if (auto logger = pixiv_downloader::utilities::Logger::GetLogger()) {
            LOG_FATAL("程序发生未处理的异常: {}", e.what());
        }

        return static_cast<int>(pixiv_downloader::core::ExitCode::UNKNOWN_ERROR);

    } catch (...) {
        // 捕获所有其他异常
        std::cerr << "程序发生未知异常" << std::endl;

        // 尝试记录到日志
        if (auto logger = pixiv_downloader::utilities::Logger::GetLogger()) {
            LOG_FATAL("程序发生未知异常");
        }

        return static_cast<int>(pixiv_downloader::core::ExitCode::UNKNOWN_ERROR);
    }
}
