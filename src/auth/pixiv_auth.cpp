#include "auth/pixiv_auth.h"
#include "config/config_manager.h"
#include "utilities/logger.h"
#include "utilities/string_utils.h"
#include <curl/curl.h>
#include <nlohmann/json.hpp>
#include <regex>

namespace pixiv_downloader {
namespace auth {

// 静态成员初始化
const std::vector<std::string> PixivAuth::REQUIRED_COOKIE_NAMES = {
    "PHPSESSID", "device_token", "privacy_policy_agreement"
};
const std::string PixivAuth::PIXIV_DOMAIN = "pixiv.net";
const std::string PixivAuth::AUTH_TEST_ENDPOINT = "https://www.pixiv.net/ajax/user/extra";

PixivAuth::PixivAuth() : auth_status_(AuthStatus::NOT_AUTHENTICATED), auth_config_(nullptr) {
    ResetUserInfo();
}

PixivAuth::PixivAuth(const config::AuthConfig& auth_config)
    : auth_status_(AuthStatus::NOT_AUTHENTICATED), auth_config_(&auth_config) {
    ResetUserInfo();
}

bool PixivAuth::SetCookieString(const std::string& cookie_string) {
    if (cookie_string.empty()) {
        LOG_ERROR("Cookie字符串为空");
        UpdateAuthStatus(AuthStatus::INVALID);
        return false;
    }

    // 验证Cookie格式
    if (!ValidateCookieFormat(cookie_string)) {
        LOG_ERROR("Cookie格式无效");
        UpdateAuthStatus(AuthStatus::INVALID);
        return false;
    }

    // 解析Cookie字符串
    cookies_ = ParseCookieString(cookie_string);
    original_cookie_string_ = cookie_string;

    // 验证必要的Cookie是否存在
    if (auth_config_ && auth_config_->strict_cookie_validation && !ValidateRequiredCookies()) {
        LOG_WARN("Cookie中缺少必要的认证信息");
        UpdateAuthStatus(AuthStatus::INSUFFICIENT_PERMISSIONS);
        return false;
    } else if (!auth_config_ && !ValidateRequiredCookies()) {
        LOG_WARN("Cookie中缺少必要的认证信息");
        UpdateAuthStatus(AuthStatus::INSUFFICIENT_PERMISSIONS);
        return false;
    }

    LOG_INFO("Cookie设置成功，包含 {} 个键值对", cookies_.size());
    UpdateAuthStatus(AuthStatus::NOT_AUTHENTICATED); // 需要进一步验证
    return true;
}

std::map<std::string, std::string> PixivAuth::ParseCookieString(const std::string& cookie_string) {
    std::map<std::string, std::string> cookies;
    
    // 分割Cookie字符串
    auto cookie_pairs = utilities::StringUtils::Split(cookie_string, ";");
    
    for (const auto& pair : cookie_pairs) {
        std::string trimmed_pair = utilities::StringUtils::Trim(pair);
        if (trimmed_pair.empty()) continue;
        
        // 查找等号分隔符
        size_t equal_pos = trimmed_pair.find('=');
        if (equal_pos == std::string::npos) continue;
        
        std::string name = utilities::StringUtils::Trim(trimmed_pair.substr(0, equal_pos));
        std::string value = utilities::StringUtils::Trim(trimmed_pair.substr(equal_pos + 1));
        
        if (!name.empty()) {
            cookies[name] = value;
        }
    }
    
    return cookies;
}

AuthStatus PixivAuth::ValidateCookie() {
    if (cookies_.empty()) {
        LOG_ERROR("未设置Cookie");
        UpdateAuthStatus(AuthStatus::NOT_AUTHENTICATED);
        return auth_status_;
    }

    // 检查是否跳过在线验证
    if (auth_config_ && auth_config_->skip_online_validation) {
        LOG_WARN("跳过在线Cookie验证（配置设置）");
        UpdateAuthStatus(AuthStatus::AUTHENTICATED);
        return auth_status_;
    }

    // 使用libcurl进行HTTP请求验证
    CURL* curl = curl_easy_init();
    if (!curl) {
        LOG_ERROR("初始化CURL失败");
        UpdateAuthStatus(AuthStatus::INVALID);
        return auth_status_;
    }

    std::string response_data;
    std::string cookie_header = GetCookieString();

    // 设置CURL选项
    curl_easy_setopt(curl, CURLOPT_URL, AUTH_TEST_ENDPOINT.c_str());
    curl_easy_setopt(curl, CURLOPT_COOKIE, cookie_header.c_str());
    curl_easy_setopt(curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
    curl_easy_setopt(curl, CURLOPT_REFERER, "https://www.pixiv.net/");
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);

    // 使用配置的超时时间
    long timeout = auth_config_ ? auth_config_->validation_timeout : 30L;
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, timeout);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, [](void* contents, size_t size, size_t nmemb, std::string* userp) -> size_t {
        size_t total_size = size * nmemb;
        userp->append(static_cast<char*>(contents), total_size);
        return total_size;
    });
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response_data);

    // 执行请求
    CURLcode res = curl_easy_perform(curl);
    long response_code = 0;
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
    curl_easy_cleanup(curl);

    if (res != CURLE_OK) {
        LOG_ERROR("Cookie验证请求失败: {}", curl_easy_strerror(res));
        UpdateAuthStatus(AuthStatus::INVALID);
        return auth_status_;
    }

    // 分析响应
    if (response_code == 200) {
        try {
            auto json_response = nlohmann::json::parse(response_data);
            if (json_response.contains("error") && json_response["error"].get<bool>()) {
                LOG_ERROR("Cookie验证失败: API返回错误");
                UpdateAuthStatus(AuthStatus::INVALID);
            } else {
                LOG_INFO("Cookie验证成功");
                UpdateAuthStatus(AuthStatus::AUTHENTICATED);
                
                // 尝试获取用户信息
                GetCurrentUserInfo();
            }
        } catch (const nlohmann::json::exception& e) {
            LOG_WARN("解析验证响应失败，但HTTP状态码正常: {}", e.what());
            UpdateAuthStatus(AuthStatus::AUTHENTICATED);
        }
    } else if (response_code == 401 || response_code == 403) {
        LOG_ERROR("Cookie已过期或权限不足 (HTTP {})", response_code);
        UpdateAuthStatus(AuthStatus::EXPIRED);
    } else {
        LOG_ERROR("Cookie验证失败 (HTTP {})", response_code);
        UpdateAuthStatus(AuthStatus::INVALID);
    }

    return auth_status_;
}

UserInfo PixivAuth::GetCurrentUserInfo() {
    if (auth_status_ != AuthStatus::AUTHENTICATED) {
        LOG_WARN("未认证状态，无法获取用户信息");
        return user_info_;
    }

    // 这里应该调用Pixiv API获取用户信息
    // 由于需要API客户端，这里先返回基本信息
    // 实际实现会在API客户端中完成
    
    return user_info_;
}

bool PixivAuth::CanViewR18Content() {
    // 检查Cookie中是否包含R18相关的设置
    // 这通常需要通过API调用来确定
    return auth_status_ == AuthStatus::AUTHENTICATED;
}

std::string PixivAuth::GetCookieString() const {
    if (cookies_.empty()) {
        return "";
    }

    std::vector<std::string> cookie_pairs;
    for (const auto& [name, value] : cookies_) {
        cookie_pairs.push_back(name + "=" + value);
    }

    return utilities::StringUtils::Join(cookie_pairs, "; ");
}

std::string PixivAuth::GetCookieValue(const std::string& name) const {
    auto it = cookies_.find(name);
    return (it != cookies_.end()) ? it->second : "";
}

bool PixivAuth::HasCookie(const std::string& name) const {
    return cookies_.find(name) != cookies_.end();
}

void PixivAuth::ClearCookies() {
    cookies_.clear();
    original_cookie_string_.clear();
    UpdateAuthStatus(AuthStatus::NOT_AUTHENTICATED);
    ResetUserInfo();
}

bool PixivAuth::HasRequiredCookies() const {
    return ValidateRequiredCookies();
}

std::string PixivAuth::AuthStatusToString(AuthStatus status) {
    switch (status) {
        case AuthStatus::NOT_AUTHENTICATED: return "未认证";
        case AuthStatus::AUTHENTICATED: return "已认证";
        case AuthStatus::EXPIRED: return "已过期";
        case AuthStatus::INVALID: return "无效";
        case AuthStatus::INSUFFICIENT_PERMISSIONS: return "权限不足";
        default: return "未知状态";
    }
}

bool PixivAuth::ValidateCookieFormat(const std::string& cookie_string) {
    if (cookie_string.empty()) {
        return false;
    }

    // 基本格式检查：应该包含键值对
    std::regex cookie_pattern(R"([^=]+=[^;]*(?:;\s*[^=]+=[^;]*)*)");
    return std::regex_match(cookie_string, cookie_pattern);
}

std::string PixivAuth::ExtractDomainFromCookie(const std::string& cookie_string) {
    (void)cookie_string; // 避免未使用参数警告
    // 简单实现，返回Pixiv域名
    return PIXIV_DOMAIN;
}

bool PixivAuth::IsCookieExpired(const std::string& cookie_string) {
    (void)cookie_string; // 避免未使用参数警告
    // 这里可以实现更复杂的过期检查逻辑
    // 目前通过API验证来确定
    return false;
}

void PixivAuth::UpdateAuthStatus(AuthStatus status) {
    auth_status_ = status;
    LOG_DEBUG("认证状态更新为: {}", AuthStatusToString(status));
}

void PixivAuth::ResetUserInfo() {
    user_info_ = UserInfo{};
}

bool PixivAuth::ValidateRequiredCookies() const {
    for (const auto& required_name : REQUIRED_COOKIE_NAMES) {
        if (!HasCookie(required_name)) {
            LOG_DEBUG("缺少必要的Cookie: {}", required_name);
            return false;
        }
    }
    return true;
}

} // namespace auth
} // namespace pixiv_downloader
